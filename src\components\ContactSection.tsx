import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent } from "@/components/ui/card";
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  Instagram,
  MessageCircle,
} from "lucide-react";

const ContactSection = () => {
  return (
    <section
      id="contact"
      className="py-20 md:py-28 px-4 bg-gradient-to-br from-brand-50/50 via-white to-wellness-50/30"
    >
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-20 animate-fade-in">
          <div className="inline-flex items-center bg-gradient-to-r from-brand-100 to-wellness-100 text-brand-700 px-4 py-2 rounded-full text-sm font-medium mb-6 shadow-soft">
            📞 Entre em Contato
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-900 mb-6 tracking-tight">
            Agende Sua <span className="text-primary">Consulta</span>
          </h2>
          <p className="text-xl md:text-2xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
            Estou aqui para ajudar você a alcançar seu bem-estar. Entre em
            contato e vamos conversar sobre suas necessidades específicas.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16">
          <div className="space-y-10 animate-fade-in">
            <div className="space-y-8">
              <div className="flex items-start space-x-6 group">
                <div className="w-16 h-16 bg-gradient-to-br from-brand-400 to-brand-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-medium group-hover:scale-110 transition-transform duration-300">
                  <MapPin className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-neutral-900 mb-3">
                    Localização
                  </h3>
                  <p className="text-lg text-neutral-600 mb-1">São Paulo, SP</p>
                  <p className="text-base text-neutral-500">
                    Consultório em região central
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-6 group">
                <div className="w-16 h-16 bg-gradient-to-br from-wellness-500 to-wellness-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-medium group-hover:scale-110 transition-transform duration-300">
                  <Clock className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-neutral-900 mb-3">
                    Horários
                  </h3>
                  <p className="text-lg text-neutral-600 mb-1">
                    Segunda a Sexta: 8h às 18h
                  </p>
                  <p className="text-base text-neutral-500">
                    Sábado: 8h às 14h
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-6 group">
                <div className="w-16 h-16 bg-gradient-to-br from-brand-500 to-brand-600 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-medium group-hover:scale-110 transition-transform duration-300">
                  <Phone className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-neutral-900 mb-3">
                    Contato
                  </h3>
                  <p className="text-lg text-neutral-600 mb-1">
                    (11) 99999-9999
                  </p>
                  <p className="text-base text-neutral-500">
                    <EMAIL>
                  </p>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-4 rounded-full font-semibold group transition-all duration-300 hover:scale-105 shadow-medium hover:shadow-large"
                onClick={() =>
                  window.open("https://wa.me/5511999999999", "_blank")
                }
              >
                <MessageCircle className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                WhatsApp
              </Button>
              <Button
                variant="outline"
                className="border-2 border-primary text-primary hover:bg-primary hover:text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 hover:scale-105"
                onClick={() =>
                  window.open("https://instagram.com/mariahblaj", "_blank")
                }
              >
                <Instagram className="mr-2 h-5 w-5" />
                Instagram
              </Button>
            </div>
          </div>

          <Card className="animate-slide-in-right bg-white/90 backdrop-blur-sm shadow-large border-none overflow-hidden">
            <CardContent className="p-10">
              <div className="mb-8">
                <h3 className="text-3xl font-bold text-neutral-900 mb-3">
                  Solicite Informações
                </h3>
                <p className="text-lg text-neutral-600">
                  Preencha o formulário e entraremos em contato em breve
                </p>
              </div>
              <form className="space-y-8">
                <div>
                  <label className="block text-base font-semibold text-neutral-700 mb-3">
                    Nome
                  </label>
                  <input
                    type="text"
                    className="w-full px-6 py-4 border-2 border-neutral-200 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 text-lg"
                    placeholder="Seu nome completo"
                  />
                </div>
                <div>
                  <label className="block text-base font-semibold text-neutral-700 mb-3">
                    Telefone
                  </label>
                  <input
                    type="tel"
                    className="w-full px-6 py-4 border-2 border-neutral-200 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 text-lg"
                    placeholder="(11) 99999-9999"
                  />
                </div>
                <div>
                  <label className="block text-base font-semibold text-neutral-700 mb-3">
                    Tratamento de Interesse
                  </label>
                  <select className="w-full px-6 py-4 border-2 border-neutral-200 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 text-lg">
                    <option value="">Selecione uma opção</option>
                    <option value="fisioterapia">Fisioterapia Pélvica</option>
                    <option value="pilates">Pilates Terapêutico</option>
                    <option value="yoga">Yoga Hormonal</option>
                    <option value="aromaterapia">Aromaterapia</option>
                    <option value="acupuntura">Acupuntura</option>
                    <option value="avaliacao">Avaliação Completa</option>
                  </select>
                </div>
                <div>
                  <label className="block text-base font-semibold text-neutral-700 mb-3">
                    Mensagem
                  </label>
                  <textarea
                    rows={5}
                    className="w-full px-6 py-4 border-2 border-neutral-200 rounded-xl focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 text-lg resize-none"
                    placeholder="Conte um pouco sobre suas necessidades..."
                  />
                </div>
                <Button
                  type="submit"
                  className="w-full bg-gradient-to-r from-brand-400 to-brand-600 hover:from-primary/90 hover:to-brand-700 text-white py-5 rounded-xl text-xl font-semibold transition-all duration-300 hover:scale-105 shadow-medium hover:shadow-large"
                >
                  Enviar Solicitação
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
