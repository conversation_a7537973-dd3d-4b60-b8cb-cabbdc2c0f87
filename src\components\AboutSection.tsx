import { Card, CardContent } from "@/components/ui/card";
import { Award, Heart, Users, Clock } from "lucide-react";

const stats = [
  { icon: Users, value: "500+", label: "Pacientes Atendidas" },
  { icon: Clock, value: "8+", label: "Anos de Experiência" },
  { icon: Heart, value: "98%", label: "Satisfação" },
  { icon: Award, value: "15+", label: "Especializações" },
];

const AboutSection = () => {
  return (
    <section
      id="about"
      className="py-16 md:py-24 px-4 bg-gradient-to-br from-brand-50/50 via-white to-wellness-50/30"
      aria-labelledby="about-heading"
    >
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 md:gap-16 items-center">
          <div className="space-y-8 md:space-y-10 animate-fade-in order-2 lg:order-1">
            <div>
              <h2
                id="about-heading"
                className="text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-900 mb-6 md:mb-8 text-center lg:text-left tracking-tight"
              >
                Sobre <span className="text-primary">Mariah Blaj</span>
              </h2>
              <div className="space-y-6 text-lg md:text-xl text-neutral-600 leading-relaxed">
                <p>
                  Fisioterapeuta especializada em saúde pélvica com formação em
                  terapias integrativas. Minha missão é proporcionar cuidado
                  humanizado e tratamentos eficazes para melhorar a qualidade de
                  vida das mulheres.
                </p>
                <p>
                  Com mais de 8 anos de experiência, combino técnicas modernas
                  de fisioterapia com abordagens holísticas como yoga hormonal,
                  aromaterapia e acupuntura, oferecendo um tratamento completo e
                  personalizado.
                </p>
                <p>
                  Acredito que cada mulher merece se sentir plena e confiante em
                  seu próprio corpo. Trabalho com dedicação para criar um
                  ambiente acolhedor onde você possa se sentir à vontade para
                  discutir suas necessidades e objetivos.
                </p>
              </div>
            </div>

            <div
              className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8"
              role="list"
              aria-label="Estatísticas profissionais"
            >
              {stats.map((stat, index) => (
                <div key={index} className="text-center" role="listitem">
                  <div className="w-14 h-14 md:w-16 md:h-16 bg-gradient-to-br from-brand-400 to-brand-600 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-medium">
                    <stat.icon
                      className="h-6 w-6 md:h-8 md:w-8 text-white"
                      aria-hidden="true"
                    />
                  </div>
                  <div
                    className="text-2xl md:text-3xl font-bold text-primary"
                    aria-label={`${stat.value} ${stat.label}`}
                  >
                    {stat.value}
                  </div>
                  <div className="text-sm md:text-base text-neutral-600 font-medium">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="relative animate-slide-in-right order-1 lg:order-2">
            <div className="relative z-10">
              <img
                src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Mariah Blaj, fisioterapeuta especializada em saúde pélvica, sorrindo em ambiente profissional"
                className="w-full h-80 md:h-96 lg:h-[500px] object-cover rounded-4xl shadow-large"
                loading="lazy"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent rounded-4xl" />
            </div>
            <div className="absolute -top-6 -right-6 w-24 h-24 md:w-32 md:h-32 bg-gradient-to-br from-brand-200 to-brand-300 rounded-full opacity-20 animate-gentle-bounce" />
            <div
              className="absolute -bottom-6 -left-6 w-20 h-20 md:w-28 md:h-28 bg-gradient-to-br from-wellness-200 to-wellness-300 rounded-full opacity-25 animate-float"
              style={{ animationDelay: "2s" }}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
