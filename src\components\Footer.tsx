import { Heart, Instagram, MessageCircle, Mail } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-gradient-to-br from-neutral-50 via-neutral-100 to-neutral-50 text-neutral-800 py-16 md:py-20 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12 md:gap-16">
          <div className="md:col-span-2 space-y-8 text-center md:text-left">
            <div className="space-y-4">
              <h3 className="text-3xl md:text-4xl font-bold text-neutral-900 font-logo">
                Maria<PERSON> Blaj
              </h3>
              <p className="text-xl text-neutral-600 leading-relaxed max-w-lg">
                Fisioterapeuta especializada em saúde pélvica, oferecendo
                cuidado integral e personalizado em São Paulo.
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex space-x-6 justify-center md:justify-start">
                <a
                  href="https://instagram.com/mariahblaj"
                  className="group w-14 h-14 bg-gradient-to-br from-brand-400 to-brand-600 rounded-2xl flex items-center justify-center text-white hover:scale-110 transition-all duration-300 shadow-medium hover:shadow-large"
                  aria-label="Instagram"
                >
                  <Instagram className="h-6 w-6 group-hover:scale-110 transition-transform" />
                </a>
                <a
                  href="https://wa.me/5511999999999"
                  className="group w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center text-white hover:scale-110 transition-all duration-300 shadow-medium hover:shadow-large"
                  aria-label="WhatsApp"
                >
                  <MessageCircle className="h-6 w-6 group-hover:scale-110 transition-transform" />
                </a>
                <a
                  href="mailto:<EMAIL>"
                  className="group w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center text-white hover:scale-110 transition-all duration-300 shadow-medium hover:shadow-large"
                  aria-label="Email"
                >
                  <Mail className="h-6 w-6 group-hover:scale-110 transition-transform" />
                </a>
              </div>

              <div className="text-center md:text-left">
                <p className="text-lg text-neutral-500 mb-2">
                  Agende sua consulta:
                </p>
                <p className="text-2xl font-semibold text-neutral-900">
                  (11) 99999-9999
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-8 text-center md:text-left">
            <h4 className="text-2xl font-bold text-neutral-900">Tratamentos</h4>
            <ul className="space-y-4 text-neutral-600">
              <li>
                <a
                  href="#services"
                  className="hover:text-primary transition-colors duration-300 flex items-center justify-center md:justify-start text-lg"
                >
                  Fisioterapia Pélvica
                </a>
              </li>
              <li>
                <a
                  href="#services"
                  className="hover:text-primary transition-colors duration-300 flex items-center justify-center md:justify-start text-lg"
                >
                  Pilates Terapêutico
                </a>
              </li>
              <li>
                <a
                  href="#services"
                  className="hover:text-primary transition-colors duration-300 flex items-center justify-center md:justify-start text-lg"
                >
                  Yoga Hormonal
                </a>
              </li>
              <li>
                <a
                  href="#services"
                  className="hover:text-primary transition-colors duration-300 flex items-center justify-center md:justify-start text-lg"
                >
                  Aromaterapia
                </a>
              </li>
              <li>
                <a
                  href="#services"
                  className="hover:text-primary transition-colors duration-300 flex items-center justify-center md:justify-start text-lg"
                >
                  Acupuntura
                </a>
              </li>
            </ul>
          </div>

          <div className="space-y-8 text-center md:text-left">
            <h4 className="text-2xl font-bold text-neutral-900">Informações</h4>
            <div className="space-y-6 text-neutral-600">
              <div className="space-y-3">
                <p className="flex items-center justify-center md:justify-start text-lg">
                  📍 São Paulo, SP
                </p>
                <p className="flex items-center justify-center md:justify-start text-lg">
                  ✉️ <EMAIL>
                </p>
              </div>
              <div className="pt-4 border-t border-neutral-200">
                <p className="font-semibold text-neutral-900 mb-3 text-lg">
                  Horários de Atendimento:
                </p>
                <p className="text-base mb-1">Segunda a Sexta: 8h às 18h</p>
                <p className="text-base">Sábado: 8h às 14h</p>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-neutral-200 mt-16 pt-10 text-center">
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 text-neutral-500">
            <span className="text-lg">
              © 2024 Mariah Blaj - Fisioterapeuta Pélvica.
            </span>
            <div className="flex items-center space-x-2">
              <span className="text-lg">Feito com</span>
              <Heart className="h-5 w-5 text-primary fill-current animate-gentle-bounce" />
              <span className="text-lg">em São Paulo</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
