import {
  Instagram,
  MessageCircle,
  Mail,
  MapPin,
  Clock,
  Phone,
  ExternalLink,
  Shield,
  FileText,
} from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer
      className="bg-neutral-900 text-neutral-100 py-16 md:py-20 px-4"
      role="contentinfo"
      aria-label="Informações da empresa e links importantes"
    >
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalBusiness",
            name: "<PERSON><PERSON> Blaj - Fisioterapeuta Pélvica",
            description:
              "Fisioterapeuta especializada em saúde pélvica, oferecendo cuidado integral e personalizado em São Paulo.",
            url: "https://mariahblaj.com.br",
            telephone: "+55-11-99999-9999",
            email: "<EMAIL>",
            address: {
              "@type": "PostalAddress",
              addressLocality: "São Paulo",
              addressRegion: "SP",
              addressCountry: "BR",
            },
            openingHours: ["Mo-Fr 08:00-18:00", "Sa 08:00-14:00"],
            medicalSpecialty: "Fisioterapia Pélvica",
            sameAs: ["https://instagram.com/mariahblaj"],
          }),
        }}
      />

      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 md:gap-16">
          {/* Company Information */}
          <div className="lg:col-span-2 space-y-8">
            <div className="space-y-6">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-white font-logo mb-4">
                  Mariah Blaj
                </h2>
                <p className="text-lg text-neutral-300 leading-relaxed max-w-lg">
                  Fisioterapeuta especializada em saúde pélvica, oferecendo
                  cuidado integral e personalizado em São Paulo.
                </p>
              </div>

              {/* Professional Contact Information */}
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-5 w-5 text-brand-400 flex-shrink-0" />
                    <span className="text-neutral-300">
                      São Paulo, SP - Região Central
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-brand-400 flex-shrink-0" />
                    <a
                      href="tel:+5511999999999"
                      className="text-neutral-300 hover:text-white transition-colors duration-300 focus-visible-ring"
                      aria-label="Ligar para (11) 99999-9999"
                    >
                      (11) 99999-9999
                    </a>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-brand-400 flex-shrink-0" />
                    <a
                      href="mailto:<EMAIL>"
                      className="text-neutral-300 hover:text-white transition-colors duration-300 focus-visible-ring"
                      aria-label="Enviar <NAME_EMAIL>"
                    >
                      <EMAIL>
                    </a>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Clock className="h-5 w-5 text-brand-400 flex-shrink-0" />
                    <div className="text-neutral-300">
                      <div>Segunda a Sexta: 8h às 18h</div>
                      <div>Sábado: 8h às 14h</div>
                    </div>
                  </div>
                </div>

                {/* Professional Social Media */}
                <div className="pt-6 border-t border-neutral-700">
                  <h3 className="text-sm font-semibold text-neutral-400 uppercase tracking-wider mb-4">
                    Redes Sociais
                  </h3>
                  <div className="flex space-x-4">
                    <a
                      href="https://instagram.com/mariahblaj"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center space-x-2 px-4 py-2 bg-neutral-800 hover:bg-neutral-700 rounded-lg transition-all duration-300 focus-visible-ring"
                      aria-label="Seguir no Instagram"
                    >
                      <Instagram className="h-4 w-4 text-neutral-400 group-hover:text-white transition-colors" />
                      <span className="text-sm text-neutral-400 group-hover:text-white transition-colors">
                        Instagram
                      </span>
                      <ExternalLink className="h-3 w-3 text-neutral-500 group-hover:text-neutral-300 transition-colors" />
                    </a>
                    <a
                      href="https://wa.me/5511999999999"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="group flex items-center space-x-2 px-4 py-2 bg-neutral-800 hover:bg-neutral-700 rounded-lg transition-all duration-300 focus-visible-ring"
                      aria-label="Conversar no WhatsApp"
                    >
                      <MessageCircle className="h-4 w-4 text-neutral-400 group-hover:text-white transition-colors" />
                      <span className="text-sm text-neutral-400 group-hover:text-white transition-colors">
                        WhatsApp
                      </span>
                      <ExternalLink className="h-3 w-3 text-neutral-500 group-hover:text-neutral-300 transition-colors" />
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Services Navigation */}
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-semibold text-white mb-6">
                Tratamentos
              </h3>
              <nav aria-label="Navegação de tratamentos">
                <ul className="space-y-3">
                  <li>
                    <a
                      href="#services"
                      className="text-neutral-300 hover:text-white transition-colors duration-300 focus-visible-ring block py-1"
                      aria-label="Saiba mais sobre Fisioterapia Pélvica"
                    >
                      Fisioterapia Pélvica
                    </a>
                  </li>
                  <li>
                    <a
                      href="#services"
                      className="text-neutral-300 hover:text-white transition-colors duration-300 focus-visible-ring block py-1"
                      aria-label="Saiba mais sobre Pilates Terapêutico"
                    >
                      Pilates Terapêutico
                    </a>
                  </li>
                  <li>
                    <a
                      href="#services"
                      className="text-neutral-300 hover:text-white transition-colors duration-300 focus-visible-ring block py-1"
                      aria-label="Saiba mais sobre Yoga Hormonal"
                    >
                      Yoga Hormonal
                    </a>
                  </li>
                  <li>
                    <a
                      href="#services"
                      className="text-neutral-300 hover:text-white transition-colors duration-300 focus-visible-ring block py-1"
                      aria-label="Saiba mais sobre Aromaterapia"
                    >
                      Aromaterapia
                    </a>
                  </li>
                  <li>
                    <a
                      href="#services"
                      className="text-neutral-300 hover:text-white transition-colors duration-300 focus-visible-ring block py-1"
                      aria-label="Saiba mais sobre Acupuntura"
                    >
                      Acupuntura
                    </a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>

          {/* Legal and Business Information */}
          <div className="space-y-8">
            <div>
              <h3 className="text-lg font-semibold text-white mb-6">
                Informações Legais
              </h3>
              <nav aria-label="Links legais e informativos">
                <ul className="space-y-3">
                  <li>
                    <a
                      href="#about"
                      className="text-neutral-300 hover:text-white transition-colors duration-300 focus-visible-ring block py-1"
                      aria-label="Sobre a profissional"
                    >
                      Sobre a Profissional
                    </a>
                  </li>
                  <li>
                    <a
                      href="#contact"
                      className="text-neutral-300 hover:text-white transition-colors duration-300 focus-visible-ring block py-1"
                      aria-label="Entre em contato"
                    >
                      Contato
                    </a>
                  </li>
                  <li>
                    <button
                      type="button"
                      className="text-neutral-300 hover:text-white transition-colors duration-300 focus-visible-ring block py-1 text-left"
                      aria-label="Política de Privacidade"
                    >
                      <Shield className="inline h-4 w-4 mr-2" />
                      Política de Privacidade
                    </button>
                  </li>
                  <li>
                    <button
                      type="button"
                      className="text-neutral-300 hover:text-white transition-colors duration-300 focus-visible-ring block py-1 text-left"
                      aria-label="Termos de Uso"
                    >
                      <FileText className="inline h-4 w-4 mr-2" />
                      Termos de Uso
                    </button>
                  </li>
                </ul>
              </nav>
            </div>

            {/* Professional Credentials */}
            <div className="pt-6 border-t border-neutral-700">
              <h4 className="text-sm font-semibold text-neutral-400 uppercase tracking-wider mb-3">
                Credenciais Profissionais
              </h4>
              <div className="space-y-2 text-sm text-neutral-400">
                <p>CREFITO-3: [Número do registro]</p>
                <p>Especialização em Fisioterapia Pélvica</p>
                <p>Formação em Pilates Terapêutico</p>
              </div>
            </div>
          </div>
        </div>

        {/* Professional Footer Bottom */}
        <div className="border-t border-neutral-700 mt-16 pt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6 text-neutral-400">
              <span className="text-sm">
                © {currentYear} Mariah Blaj - Fisioterapeuta Pélvica. Todos os
                direitos reservados.
              </span>
              <span className="text-sm">CNPJ: XX.XXX.XXX/XXXX-XX</span>
            </div>

            <div className="flex items-center space-x-4 text-neutral-400">
              <span className="text-sm">
                Desenvolvido com tecnologia moderna
              </span>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-brand-400 rounded-full animate-professional-pulse"></div>
                <span className="text-xs">São Paulo, Brasil</span>
              </div>
            </div>
          </div>

          {/* Accessibility Statement */}
          <div className="mt-6 pt-6 border-t border-neutral-800 text-center">
            <p className="text-xs text-neutral-500">
              Este site foi desenvolvido seguindo as diretrizes de
              acessibilidade WCAG 2.1 AA para garantir uma experiência inclusiva
              para todos os usuários.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
